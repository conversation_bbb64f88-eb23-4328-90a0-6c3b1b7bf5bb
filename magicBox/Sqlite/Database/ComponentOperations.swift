import SQLite
import Foundation

// MARK: - Component 相关操作
extension DatabaseManager {
    
    // MARK: - 创建模板组件
    func createTemplateComponent(_ component: TemplateComponent) throws -> Int64 {
        let insert = Tables.templateComponents.insert(
            TemplateComponentColumns.templateId <- component.templateId,
            TemplateComponentColumns.componentName <- component.componentName,
            TemplateComponentColumns.componentTitle <- component.componentTitle,
            TemplateComponentColumns.componentType <- component.componentType,
            TemplateComponentColumns.isRequired <- component.isRequired,
            TemplateComponentColumns.isMultiValue <- component.isMultiValue,
            TemplateComponentColumns.hasPredefinedOptions <- component.hasPredefinedOptions,
            TemplateComponentColumns.allowCustomValue <- component.allowCustomValue,
            TemplateComponentColumns.sortOrder <- component.sortOrder,
            TemplateComponentColumns.defaultValue <- component.defaultValue
        )
        return try db!.run(insert)
    }
    
    // MARK: - 获取模板的所有组件
    func getTemplateComponents(templateId: Int64) throws -> [TemplateComponent] {
        var result: [TemplateComponent] = []
        let query = Tables.templateComponents.filter(TemplateComponentColumns.templateId == templateId).order(TemplateComponentColumns.sortOrder)
        for row in try db!.prepare(query) {
            result.append(TemplateComponent(
                id: row[TemplateComponentColumns.id],
                templateId: row[TemplateComponentColumns.templateId],
                componentName: row[TemplateComponentColumns.componentName],
                componentTitle: row[TemplateComponentColumns.componentTitle],
                componentType: row[TemplateComponentColumns.componentType],
                isRequired: row[TemplateComponentColumns.isRequired],
                isMultiValue: row[TemplateComponentColumns.isMultiValue],
                hasPredefinedOptions: row[TemplateComponentColumns.hasPredefinedOptions],
                allowCustomValue: row[TemplateComponentColumns.allowCustomValue],
                sortOrder: row[TemplateComponentColumns.sortOrder],
                defaultValue: row[TemplateComponentColumns.defaultValue]
            ))
        }
        return result
    }
    
    // MARK: - 根据ID获取组件
    func getTemplateComponent(id: Int64) throws -> TemplateComponent? {
        let query = Tables.templateComponents.filter(TemplateComponentColumns.id == id)
        if let row = try db!.pluck(query) {
            return TemplateComponent(
                id: row[TemplateComponentColumns.id],
                templateId: row[TemplateComponentColumns.templateId],
                componentName: row[TemplateComponentColumns.componentName],
                componentTitle: row[TemplateComponentColumns.componentTitle],
                componentType: row[TemplateComponentColumns.componentType],
                isRequired: row[TemplateComponentColumns.isRequired],
                isMultiValue: row[TemplateComponentColumns.isMultiValue],
                hasPredefinedOptions: row[TemplateComponentColumns.hasPredefinedOptions],
                allowCustomValue: row[TemplateComponentColumns.allowCustomValue],
                sortOrder: row[TemplateComponentColumns.sortOrder],
                defaultValue: row[TemplateComponentColumns.defaultValue]
            )
        }
        return nil
    }
    
    // MARK: - 更新组件
    func updateTemplateComponent(id: Int64, component: TemplateComponent) throws -> Bool {
        let comp = Tables.templateComponents.filter(TemplateComponentColumns.id == id)
        let update = comp.update(
            TemplateComponentColumns.componentName <- component.componentName,
            TemplateComponentColumns.componentTitle <- component.componentTitle,
            TemplateComponentColumns.componentType <- component.componentType,
            TemplateComponentColumns.isRequired <- component.isRequired,
            TemplateComponentColumns.isMultiValue <- component.isMultiValue,
            TemplateComponentColumns.hasPredefinedOptions <- component.hasPredefinedOptions,
            TemplateComponentColumns.allowCustomValue <- component.allowCustomValue,
            TemplateComponentColumns.sortOrder <- component.sortOrder,
            TemplateComponentColumns.defaultValue <- component.defaultValue
        )
        return try db!.run(update) > 0
    }
    
    // MARK: - 删除组件
    func deleteTemplateComponent(id: Int64) throws -> Bool {
        let component = Tables.templateComponents.filter(TemplateComponentColumns.id == id)
        return try db!.run(component.delete()) > 0
    }
    
    // MARK: - 创建组件选项
    func createComponentOption(_ option: ComponentOption) throws -> Int64 {
        let insert = Tables.componentOptions.insert(
            ComponentOptionColumns.componentId <- option.componentId,
            ComponentOptionColumns.optionValue <- option.optionValue,
            ComponentOptionColumns.optionDisplay <- option.optionDisplay,
            ComponentOptionColumns.optionColor <- option.optionColor,
            ComponentOptionColumns.isActive <- option.isActive,
            ComponentOptionColumns.sortOrder <- option.sortOrder,
            ComponentOptionColumns.createdAt <- option.createdAt
        )
        return try db!.run(insert)
    }
    
    // MARK: - 获取组件的所有选项
    func getComponentOptions(componentId: Int64) throws -> [ComponentOption] {
        var result: [ComponentOption] = []
        let query = Tables.componentOptions.filter(ComponentOptionColumns.componentId == componentId && ComponentOptionColumns.isActive == true).order(ComponentOptionColumns.sortOrder)
        for row in try db!.prepare(query) {
            result.append(ComponentOption(
                id: row[ComponentOptionColumns.id],
                componentId: row[ComponentOptionColumns.componentId],
                optionValue: row[ComponentOptionColumns.optionValue],
                optionDisplay: row[ComponentOptionColumns.optionDisplay],
                optionColor: row[ComponentOptionColumns.optionColor],
                isActive: row[ComponentOptionColumns.isActive],
                sortOrder: row[ComponentOptionColumns.sortOrder],
                createdAt: row[ComponentOptionColumns.createdAt]
            ))
        }
        return result
    }
    
    // MARK: - 获取所有组件选项（包括禁用的）
    func getAllComponentOptions(componentId: Int64) throws -> [ComponentOption] {
        var result: [ComponentOption] = []
        let query = Tables.componentOptions.filter(ComponentOptionColumns.componentId == componentId).order(ComponentOptionColumns.sortOrder)
        for row in try db!.prepare(query) {
            result.append(ComponentOption(
                id: row[ComponentOptionColumns.id],
                componentId: row[ComponentOptionColumns.componentId],
                optionValue: row[ComponentOptionColumns.optionValue],
                optionDisplay: row[ComponentOptionColumns.optionDisplay],
                optionColor: row[ComponentOptionColumns.optionColor],
                isActive: row[ComponentOptionColumns.isActive],
                sortOrder: row[ComponentOptionColumns.sortOrder],
                createdAt: row[ComponentOptionColumns.createdAt]
            ))
        }
        return result
    }
    
    // MARK: - 更新组件选项
    func updateComponentOption(id: Int64, option: ComponentOption) throws -> Bool {
        let opt = Tables.componentOptions.filter(ComponentOptionColumns.id == id)
        let update = opt.update(
            ComponentOptionColumns.optionValue <- option.optionValue,
            ComponentOptionColumns.optionDisplay <- option.optionDisplay,
            ComponentOptionColumns.optionColor <- option.optionColor,
            ComponentOptionColumns.isActive <- option.isActive,
            ComponentOptionColumns.sortOrder <- option.sortOrder
        )
        return try db!.run(update) > 0
    }
    
    // MARK: - 删除组件选项
    func deleteComponentOption(id: Int64) throws -> Bool {
        let option = Tables.componentOptions.filter(ComponentOptionColumns.id == id)
        return try db!.run(option.delete()) > 0
    }
    
    // MARK: - 禁用/启用组件选项
    func toggleComponentOption(id: Int64, isActive: Bool) throws -> Bool {
        let option = Tables.componentOptions.filter(ComponentOptionColumns.id == id)
        let update = option.update(ComponentOptionColumns.isActive <- isActive)
        return try db!.run(update) > 0
    }
    
    // MARK: - 检查组件名是否存在
    func componentNameExists(_ name: String, templateId: Int64, excludeId: Int64? = nil) throws -> Bool {
        var query = Tables.templateComponents.filter(TemplateComponentColumns.componentName == name && TemplateComponentColumns.templateId == templateId)
        if let excludeId = excludeId {
            query = query.filter(TemplateComponentColumns.id != excludeId)
        }
        return try db!.scalar(query.count) > 0
    }
    
    // MARK: - 获取组件的最大排序值
    func getMaxSortOrder(componentId: Int64) throws -> Int {
        let query = Tables.componentOptions.filter(ComponentOptionColumns.componentId == componentId).select(ComponentOptionColumns.sortOrder.max)
        if let maxSort = try db!.scalar(query) {
            return maxSort ?? 0
        }
        return 0
    }
} 