import SQLite
import Foundation

// MARK: - Item 相关操作
extension DatabaseManager {
    
    // MARK: - 创建物品
    func createItem(_ item: Item) throws -> Int64 {
        let insert = Tables.items.insert(
            ItemColumns.templateId <- item.templateId,
            ItemColumns.createdAt <- item.createdAt,
            ItemColumns.updatedAt <- item.updatedAt
        )
        return try db!.run(insert)
    }
    
    // MARK: - 获取所有物品
    func getAllItems() throws -> [Item] {
        var result: [Item] = []
        for row in try db!.prepare(Tables.items.order(ItemColumns.createdAt.desc)) {
            result.append(Item(
                id: row[ItemColumns.id],
                templateId: row[ItemColumns.templateId],
                createdAt: row[ItemColumns.createdAt],
                updatedAt: row[ItemColumns.updatedAt]
            ))
        }
        return result
    }
    
    // MARK: - 根据模板ID获取物品
    func getItemsByTemplate(templateId: Int64) throws -> [Item] {
        var result: [Item] = []
        let query = Tables.items.filter(ItemColumns.templateId == templateId).order(ItemColumns.createdAt.desc)
        for row in try db!.prepare(query) {
            result.append(Item(
                id: row[ItemColumns.id],
                templateId: row[ItemColumns.templateId],
                createdAt: row[ItemColumns.createdAt],
                updatedAt: row[ItemColumns.updatedAt]
            ))
        }
        return result
    }
    
    // MARK: - 根据ID获取物品
    func getItem(id: Int64) throws -> Item? {
        let query = Tables.items.filter(ItemColumns.id == id)
        if let row = try db!.pluck(query) {
            return Item(
                id: row[ItemColumns.id],
                templateId: row[ItemColumns.templateId],
                createdAt: row[ItemColumns.createdAt],
                updatedAt: row[ItemColumns.updatedAt]
            )
        }
        return nil
    }
    
    // MARK: - 更新物品
    func updateItem(id: Int64) throws -> Bool {
        let item = Tables.items.filter(ItemColumns.id == id)
        let update = item.update(ItemColumns.updatedAt <- Date())
        return try db!.run(update) > 0
    }
    
    // MARK: - 删除物品
    func deleteItem(id: Int64) throws -> Bool {
        let item = Tables.items.filter(ItemColumns.id == id)
        return try db!.run(item.delete()) > 0
    }
    
    // MARK: - 保存物品属性值
    func saveItemAttributeValue(_ value: ItemAttributeValue) throws -> Int64 {
        let insert = Tables.itemAttributeValues.insert(
            ItemAttributeValueColumns.itemId <- value.itemId,
            ItemAttributeValueColumns.componentId <- value.componentId,
            ItemAttributeValueColumns.value <- value.value,
            ItemAttributeValueColumns.sortOrder <- value.sortOrder
        )
        return try db!.run(insert)
    }
    
    // MARK: - 保存物品属性值（批量，支持多值）
    func saveItemAttributeValues(itemId: Int64, componentId: Int64, values: [String]) throws {
        // 先删除旧值
        let deleteQuery = Tables.itemAttributeValues.filter(ItemAttributeValueColumns.itemId == itemId && ItemAttributeValueColumns.componentId == componentId)
        try db!.run(deleteQuery.delete())
        
        // 插入新值
        for (index, value) in values.enumerated() {
            let attributeValue = ItemAttributeValue(
                itemId: itemId,
                componentId: componentId,
                value: value,
                sortOrder: index + 1
            )
            try saveItemAttributeValue(attributeValue)
        }
        
        // 更新物品的修改时间
        try updateItem(id: itemId)
    }
    
    // MARK: - 获取物品的所有属性值
    func getItemAttributeValues(itemId: Int64) throws -> [ItemAttributeValue] {
        var result: [ItemAttributeValue] = []
        let query = Tables.itemAttributeValues.filter(ItemAttributeValueColumns.itemId == itemId).order(ItemAttributeValueColumns.componentId, ItemAttributeValueColumns.sortOrder)
        for row in try db!.prepare(query) {
            result.append(ItemAttributeValue(
                id: row[ItemAttributeValueColumns.id],
                itemId: row[ItemAttributeValueColumns.itemId],
                componentId: row[ItemAttributeValueColumns.componentId],
                value: row[ItemAttributeValueColumns.value],
                sortOrder: row[ItemAttributeValueColumns.sortOrder]
            ))
        }
        return result
    }
    
    // MARK: - 获取物品指定组件的属性值
    func getItemAttributeValues(itemId: Int64, componentId: Int64) throws -> [ItemAttributeValue] {
        var result: [ItemAttributeValue] = []
        let query = Tables.itemAttributeValues.filter(ItemAttributeValueColumns.itemId == itemId && ItemAttributeValueColumns.componentId == componentId).order(ItemAttributeValueColumns.sortOrder)
        for row in try db!.prepare(query) {
            result.append(ItemAttributeValue(
                id: row[ItemAttributeValueColumns.id],
                itemId: row[ItemAttributeValueColumns.itemId],
                componentId: row[ItemAttributeValueColumns.componentId],
                value: row[ItemAttributeValueColumns.value],
                sortOrder: row[ItemAttributeValueColumns.sortOrder]
            ))
        }
        return result
    }
    
    // MARK: - 获取物品的详细信息（包含属性值和选项显示名称）
    func getItemDetails(itemId: Int64) throws -> [(component: TemplateComponent, values: [(value: String, display: String?, color: String?)])] {
        // 获取物品
        guard let item = try getItem(id: itemId) else { return [] }
        
        // 获取模板组件
        let components = try getTemplateComponents(templateId: item.templateId)
        
        var result: [(component: TemplateComponent, values: [(value: String, display: String?, color: String?)])] = []
        
        for component in components {
            // 获取该组件的属性值
            let values = try getItemAttributeValues(itemId: itemId, componentId: component.id!)
            
            var displayValues: [(value: String, display: String?, color: String?)] = []
            
            for value in values {
                // 查找对应的选项显示名称
                var matchedOption: ComponentOption?
                if component.hasPredefinedOptions {
                    let options = try getComponentOptions(componentId: component.id!)
                    matchedOption = options.first { $0.optionValue == value.value }
                }
                
                displayValues.append((
                    value: value.value,
                    display: matchedOption?.optionDisplay,
                    color: matchedOption?.optionColor
                ))
            }
            
            result.append((component: component, values: displayValues))
        }
        
        return result
    }
    
    // MARK: - 删除物品的指定属性值
    func deleteItemAttributeValue(id: Int64) throws -> Bool {
        let value = Tables.itemAttributeValues.filter(ItemAttributeValueColumns.id == id)
        return try db!.run(value.delete()) > 0
    }
    
    // MARK: - 删除物品的指定组件的所有属性值
    func deleteItemAttributeValues(itemId: Int64, componentId: Int64) throws -> Bool {
        let values = Tables.itemAttributeValues.filter(ItemAttributeValueColumns.itemId == itemId && ItemAttributeValueColumns.componentId == componentId)
        return try db!.run(values.delete()) > 0
    }
    
    // MARK: - 搜索物品
    func searchItems(keyword: String) throws -> [Item] {
        var result: [Item] = []
        
        // 通过属性值搜索
        let query = Tables.items
            .join(Tables.itemAttributeValues, on: ItemColumns.id == ItemAttributeValueColumns.itemId)
            .filter(ItemAttributeValueColumns.value.like("%\(keyword)%"))
            .select(distinct: Tables.items[ItemColumns.id], Tables.items[ItemColumns.templateId], Tables.items[ItemColumns.createdAt], Tables.items[ItemColumns.updatedAt])
            .order(ItemColumns.createdAt.desc)
        
        for row in try db!.prepare(query) {
            result.append(Item(
                id: row[ItemColumns.id],
                templateId: row[ItemColumns.templateId],
                createdAt: row[ItemColumns.createdAt],
                updatedAt: row[ItemColumns.updatedAt]
            ))
        }
        
        return result
    }
    
    // MARK: - 统计物品数量
    func getItemCount() throws -> Int {
        return try db!.scalar(Tables.items.count)
    }
    
    // MARK: - 根据模板统计物品数量
    func getItemCountByTemplate(templateId: Int64) throws -> Int {
        return try db!.scalar(Tables.items.filter(ItemColumns.templateId == templateId).count)
    }
} 