import SQLite
import Foundation

// MARK: - 数据库管理器
class DatabaseManager {
    internal var db: Connection?
    private let dbPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first!
    
    // 单例
    static let shared = DatabaseManager()
    
    private init() {
        do {
            let path = "\(dbPath)/magicbox.sqlite3"
            db = try Connection(path)
            print("Database path: \(path)")
            createTables()
        } catch {
            print("Database connection failed: \(error)")
        }
    }
    
    // MARK: - 获取数据库连接
    func getConnection() -> Connection? {
        return db
    }
    
    // MARK: - 创建所有表
    private func createTables() {
        do {
            try createTemplatesTable()
            try createTemplateComponentsTable()
            try createComponentOptionsTable()
            try createItemsTable()
            try createItemAttributeValuesTable()
            print("All tables created successfully")
        } catch {
            print("Create tables failed: \(error)")
        }
    }
    
    // MARK: - 数据库重置
    func resetDatabase() {
        do {
            try db?.run(Tables.itemAttributeValues.drop(ifExists: true))
            try db?.run(Tables.items.drop(ifExists: true))
            try db?.run(Tables.componentOptions.drop(ifExists: true))
            try db?.run(Tables.templateComponents.drop(ifExists: true))
            try db?.run(Tables.templates.drop(ifExists: true))
            createTables()
            print("Database reset successfully")
        } catch {
            print("Database reset failed: \(error)")
        }
    }
    
    // MARK: - 检查表是否存在
    private func tableExists(_ tableName: String) -> Bool {
        do {
            let count = try db?.scalar("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=?", tableName) as? Int64
            return count ?? 0 > 0
        } catch {
            return false
        }
    }
    
    // MARK: - 数据库信息
    func getDatabaseInfo() -> [String: Any] {
        var info: [String: Any] = [:]
        
        do {
            if let db = db {
                info["path"] = dbPath + "/magicbox.sqlite3"
                info["version"] = try db.scalar("SELECT sqlite_version()") as? String ?? "Unknown"
                
                // 统计各表的记录数
                if tableExists("templates") {
                    info["templates_count"] = try db.scalar(Tables.templates.count) ?? 0
                }
                if tableExists("template_components") {
                    info["template_components_count"] = try db.scalar(Tables.templateComponents.count) ?? 0
                }
                if tableExists("component_options") {
                    info["component_options_count"] = try db.scalar(Tables.componentOptions.count) ?? 0
                }
                if tableExists("items") {
                    info["items_count"] = try db.scalar(Tables.items.count) ?? 0
                }
                if tableExists("item_attribute_values") {
                    info["item_attribute_values_count"] = try db.scalar(Tables.itemAttributeValues.count) ?? 0
                }
            }
        } catch {
            print("Get database info failed: \(error)")
        }
        
        return info
    }
} 