import SQLite
import Foundation

// MARK: - 表定义
struct Tables {
    static let templates = Table("templates")
    static let templateComponents = Table("template_components")
    static let componentOptions = Table("component_options")
    static let items = Table("items")
    static let itemAttributeValues = Table("item_attribute_values")
}

// MARK: - Templates 表字段
struct TemplateColumns {
    static let id = SQLite.Expression<Int64>("id")
    static let name = SQLite.Expression<String>("name")
    static let description = SQLite.Expression<String?>("description")
    static let createdAt = SQLite.Expression<Date>("created_at")
    static let updatedAt = SQLite.Expression<Date>("updated_at")
}

// MARK: - Template Components 表字段
struct TemplateComponentColumns {
    static let id = SQLite.Expression<Int64>("id")
    static let templateId = SQLite.Expression<Int64>("template_id")
    static let componentName = SQLite.Expression<String>("component_name")
    static let componentTitle = SQLite.Expression<String>("component_title")
    static let componentType = SQLite.Expression<String>("component_type")
    static let isRequired = SQLite.Expression<Bool>("is_required")
    static let isMultiValue = SQLite.Expression<Bool>("is_multi_value")
    static let hasPredefinedOptions = SQLite.Expression<Bool>("has_predefined_options")
    static let allowCustomValue = SQLite.Expression<Bool>("allow_custom_value")
    static let sortOrder = SQLite.Expression<Int>("sort_order")
    static let defaultValue = SQLite.Expression<String?>("default_value")
}

// MARK: - Component Options 表字段
struct ComponentOptionColumns {
    static let id = SQLite.Expression<Int64>("id")
    static let componentId = SQLite.Expression<Int64>("component_id")
    static let optionValue = SQLite.Expression<String>("option_value")
    static let optionDisplay = SQLite.Expression<String>("option_display")
    static let optionColor = SQLite.Expression<String?>("option_color")
    static let isActive = SQLite.Expression<Bool>("is_active")
    static let sortOrder = SQLite.Expression<Int>("sort_order")
    static let createdAt = SQLite.Expression<Date>("created_at")
}

// MARK: - Items 表字段
struct ItemColumns {
    static let id = SQLite.Expression<Int64>("id")
    static let templateId = SQLite.Expression<Int64>("template_id")
    static let createdAt = SQLite.Expression<Date>("created_at")
    static let updatedAt = SQLite.Expression<Date>("updated_at")
}

// MARK: - Item Attribute Values 表字段
struct ItemAttributeValueColumns {
    static let id = SQLite.Expression<Int64>("id")
    static let itemId = SQLite.Expression<Int64>("item_id")
    static let componentId = SQLite.Expression<Int64>("component_id")
    static let value = SQLite.Expression<String>("value")
    static let sortOrder = SQLite.Expression<Int>("sort_order")
}